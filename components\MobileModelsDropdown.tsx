"use client";

import { useState, useContext } from 'react';
import Image from 'next/image';
import { ChevronUp, ChevronDown } from 'lucide-react';
import ModelsContext from '@/context/ModelsContext';
import { modelLogos } from '@/app/config/defaults';

interface MobileModelsDropdownProps {
  config: any;
  currentModel: string;
}

export default function MobileModelsDropdown({ config, currentModel }: MobileModelsDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { selectedModel, setSelectedModel } = useContext(ModelsContext);
  
  const currentDisplayModel = selectedModel || currentModel;
  const currentModelLabel = modelLogos[currentDisplayModel as keyof typeof modelLogos]?.label || "Models";
  
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleModelSelect = (model: string) => {
    if (model !== selectedModel) {
      setSelectedModel(model);
    }
    setIsOpen(false);
  };

  // Get the current model logo component
  const CurrentLogo = currentDisplayModel && modelLogos[currentDisplayModel as keyof typeof modelLogos]?.logo;
  
  // Get model list from config or use default list
  const modelList = config?.data?.siteType?.split(",") || 
    ["chatgpt", "claude", "mj", "grok", "sora"];

  return (
    <div className="relative w-full md:hidden">
      <button 
        onClick={toggleDropdown}
        className="bg-zinc-800 text-white w-full px-4 py-1 rounded-md flex items-center justify-center"
      >
        <div className="flex items-center space-x-2">
          {CurrentLogo && <CurrentLogo style={{ color: modelLogos[currentDisplayModel as keyof typeof modelLogos].logoColor }} size={20} />}
          <span>{currentModelLabel}</span>
        </div>
        {isOpen ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
      </button>
      
      {isOpen && (
        <div className="absolute top-full left-0 w-full bg-black border border-white/10 rounded-md mt-1 z-50">
          <div className="p-4 space-y-4">
            {modelList.map((model: string) => {
              // Make sure model exists in modelLogos config
              if (!modelLogos[model as keyof typeof modelLogos]) {
                return null;
              }
              
              const ModelLogo = modelLogos[model as keyof typeof modelLogos]?.logo;
              const modelLabel = modelLogos[model as keyof typeof modelLogos]?.label;
              
              return (
                <div 
                  key={model}
                  className="flex items-start space-x-3 p-3 rounded-md hover:bg-zinc-800 cursor-pointer"
                  onClick={() => handleModelSelect(model)}
                >
                  <div className="mt-1">
                    {ModelLogo && <ModelLogo style={{ color: modelLogos[model as keyof typeof modelLogos].logoColor }} size={20} />}
                  </div>
                  <div>
                    <p className="text-white">{modelLabel}</p>
                    <p className="text-gray-400 text-sm">Access {modelLabel} models and features</p>
                  </div>
                  {currentDisplayModel === model && (
                    <div className="ml-auto">
                      <span className="text-white">✓</span>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
} 