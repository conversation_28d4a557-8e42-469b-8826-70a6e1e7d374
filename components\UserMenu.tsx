"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';

interface UserMenuProps {
  username: string;
}

export function UserMenu({ username }: UserMenuProps) {
  const [menuOpen, setMenuOpen] = useState(false);
  const router = useRouter();

  const handleLogout = () => {
    document.cookie = "share-session=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    router.push("/login");
  };

  return (
    <div className="relative">
      <span
        className="text-white font-semibold text-sm cursor-pointer"
        onClick={() => setMenuOpen(!menuOpen)}
      >
        {username}
      </span>
      {menuOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-black text-white rounded shadow-lg z-50">
          <ul className="text-sm">
            <li
              onClick={handleLogout}
              className="px-4 py-2 cursor-pointer hover:bg-gray-600"
            >
              Logout
            </li>
          </ul>
        </div>
      )}
    </div>
  );
} 