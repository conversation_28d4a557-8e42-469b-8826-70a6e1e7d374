@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #000;
    --foreground: #ededed;
    --dark-gray: #18181b;
    --model-card-color: #33353b;
    --pink-color: #9a7cdf;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

@keyframes marquee-right {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
}
@keyframes marquee-left {
  0% { transform: translateX(-50%); }
  100% { transform: translateX(0); }
}
.animate-marquee-right {
  animation: marquee-right 20s linear infinite;
}
.animate-marquee-left {
  animation: marquee-left 20s linear infinite;
}
.animate-marquee-right:hover, .animate-marquee-left:hover {
  animation-play-state: paused;
}