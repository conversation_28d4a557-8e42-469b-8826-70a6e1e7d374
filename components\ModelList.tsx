"use client";

import { modelLogos } from "@/app/config/defaults";
import SidebarLink from "@/components/SidebarLink";
import { useContext } from "react";
import ModelsContext from "@/context/ModelsContext";

interface Config {
  data: {
    siteType: string;
  };
}

interface ModelsListProps {
  config: Config | null;
  currentModel: string;
  onModelSelect?: (model: string) => void;
  isLoading?: boolean;
}

function ModelsList({ config, currentModel, onModelSelect, isLoading = false }: ModelsListProps) {
  const { selectedModel } = useContext(ModelsContext);

  const currentSelectedModel = selectedModel || currentModel;

  // If config is null, use a default list of models
  const modelList = config?.data?.siteType?.split(",") || 
    ["chatgpt", "claude", "mj", "grok", "sora"];
  
  return (
    <ul className="space-y-3 text-sm">
      {modelList.map((model: string) => {
        // Make sure model exists in our modelLogos config
        if (!modelLogos[model as keyof typeof modelLogos]) {
          return null;
        }
        
        return (
          <SidebarLink
            key={model}
            href={`/models/${model}`}
            model={model}
            Icon={modelLogos[model as keyof typeof modelLogos].logo}
            label={modelLogos[model as keyof typeof modelLogos].label}
            logoColor={modelLogos[model as keyof typeof modelLogos].logoColor}
            isActive={currentSelectedModel === model}
            onClick={onModelSelect ? () => onModelSelect(model) : undefined}
            disabled={false}
          />
        );
      })}
    </ul>
  );
}

export default ModelsList;