"use client";

import Image from "next/image";
import React, { useContext, useState } from "react";
import Avatar from "@/public/avatar.svg";
import UserContext from "@/context/UserContext";
import Link from "next/link";

function Header() {
  const { user } = useContext(UserContext);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleLogout = () => {
    document.cookie = "share-session=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=" + process.env.NEXT_PUBLIC_DOMAIN_NAME;
    window.location.href = "/login";
  };

  return (
    <header className="backdrop-blur fixed inset-x-0 top-0 h-16 border-b border-slate-900/10 dark:border-slate-300/10 z-50 flex flex-col items-center justify-around">
      <div className="flex items-center justify-between max-w-[100rem] flex-1 w-full px-4 sm:pr-4 relative">
        <button
          type="button"
          aria-haspopup="dialog"
          aria-expanded={isMobileMenuOpen}
          aria-controls="mobile-menu"
          data-state={isMobileMenuOpen ? "open" : "closed"}
          className="md:hidden"
          onClick={toggleMobileMenu}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="lucide lucide-align-justify w-5 h-5"
          >
            <line x1="3" x2="21" y1="6" y2="6"></line>
            <line x1="3" x2="21" y1="12" y2="12"></line>
            <line x1="3" x2="21" y1="18" y2="18"></line>
          </svg>
        </button>
        <div className="relative flex items-center ml-auto">
          <nav className="hidden md:block text-sm leading-6 font-semibold text-slate-700 dark:text-slate-200">
            <ul className="flex space-x-8">
              <Link className="text-primary" href="/models">
                Select Service
              </Link>
              <Link
                className="text-default-600 hover:text-default-400 hover:dark:text-default-500"
                href="/notice"
              >
                System Announcement
              </Link>
            </ul>
          </nav>
          <div className="flex items-center lg:border-l gap-2 border-slate-300 ml-6 pl-6 dark:border-slate-600 h-5">
            <div
              data-slot="trigger"
              aria-haspopup="true"
              aria-expanded={isDropdownOpen}
              id="react-aria6884208273-:rm:"
              className="z-10 aria-expanded:scale-[0.97] aria-expanded:opacity-70 subpixel-antialiased cursor-pointer"
              onClick={toggleDropdown}
            >
              <Image src={Avatar} alt="Avatar" width={24} height={24} />
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div 
          id="mobile-menu"
          className="absolute z-[100000] bg-[#18181b] rounded-lg shadow-lg top-16 left-4 w-[calc(100%-2rem)]"
        >
          <div className="p-4">
            <ul className="flex flex-col space-y-4 text-sm leading-6 font-semibold text-slate-200">
              <li>
                <a className="text-primary block py-2" href="/models">
                  Select Service
                </a>
              </li>
              <li>
                <a
                  className="text-default-600 hover:text-default-400 hover:dark:text-default-500 block py-2"
                  href="/notice"
                >
                  System Announcement
                </a>
              </li>
            </ul>
          </div>
        </div>
      )}
      
      {isDropdownOpen && (
        <div className="absolute z-[100000] max-h-[386px] right-20 top-54 bg-[#18181b] rounded-lg" style={{
          position: "absolute",
          zIndex: 100000,
          maxHeight: 386,
          right: 20,
          top: 54
        }}>
          <div>
            <div style={{
              opacity: 1,
              transform: "scale(1)",
              transformOrigin: "100% 0% 0px"
            }}>
              <div
                data-slot="base"
                data-open="true"
                data-placement="bottom-end"
                role="dialog"
                tabIndex={-1}
                className="z-0 relative bg-transparent before:content-[''] before:hidden before:z-[-1] before:absolute before:rotate-45 before:w-2.5 before:h-2.5 before:rounded-sm data-[arrow=true]:before:block data-[placement=top]:before:-bottom-[calc(theme(spacing.5)/4_-_1.5px)] data-[placement=top]:before:left-1/2 data-[placement=top]:before:-translate-x-1/2 data-[placement=top-start]:before:-bottom-[calc(theme(spacing.5)/4_-_1.5px)] data-[placement=top-start]:before:left-3 data-[placement=top-end]:before:-bottom-[calc(theme(spacing.5)/4_-_1.5px)] data-[placement=top-end]:before:right-3 data-[placement=bottom]:before:-top-[calc(theme(spacing.5)/4_-_1.5px)] data-[placement=bottom]:before:left-1/2 data-[placement=bottom]:before:-translate-x-1/2 data-[placement=bottom-start]:before:-top-[calc(theme(spacing.5)/4_-_1.5px)] data-[placement=bottom-start]:before:left-3 data-[placement=bottom-end]:before:-top-[calc(theme(spacing.5)/4_-_1.5px)] data-[placement=bottom-end]:before:right-3 data-[placement=left]:before:-right-[calc(theme(spacing.5)/4_-_2px)] data-[placement=left]:before:top-1/2 data-[placement=left]:before:-translate-y-1/2 data-[placement=left-start]:before:-right-[calc(theme(spacing.5)/4_-_3px)] data-[placement=left-start]:before:top-1/4 data-[placement=left-end]:before:-right-[calc(theme(spacing.5)/4_-_3px)] data-[placement=left-end]:before:bottom-1/4 data-[placement=right]:before:-left-[calc(theme(spacing.5)/4_-_2px)] data-[placement=right]:before:top-1/2 data-[placement=right]:before:-translate-y-1/2 data-[placement=right-start]:before:-left-[calc(theme(spacing.5)/4_-_3px)] data-[placement=right-start]:before:top-1/4 data-[placement=right-end]:before:-left-[calc(theme(spacing.5)/4_-_3px)] data-[placement=right-end]:before:bottom-1/4 outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 before:bg-content1 before:shadow-small"
                style={{ outline: "none" }}
              >
                <div
                  data-slot="content"
                  data-open="true"
                  data-placement="bottom-end"
                  className="z-10 inline-flex flex-col items-center justify-center subpixel-antialiased outline-none box-border text-small bg-content1 rounded-large shadow-medium w-full p-1 min-w-[200px]"
                >
                  <span data-focus-scope-start="true" hidden={true}></span>
                  <div
                    data-slot="base"
                    className="w-full relative flex flex-col gap-1 px-2 py-1"
                  >
                    <ul
                      data-slot="list"
                      className="w-full flex flex-col gap-0.5 outline-none"
                      id="react-aria7472048721-:rn:"
                      aria-labelledby="react-aria7472048721-:rm:"
                      role="menu"
                      tabIndex={-1}
                    >
                      <li
                        data-slot="base"
                        role="presentation"
                        className="relative mb-2"
                      >
                        <span
                          id="react-aria7472048721-:r28:"
                          role="presentation"
                          className="text-tiny text-foreground-500 text-[#a1a1aa] text-xs"
                          data-slot="heading"
                        >
                          Personal Information
                        </span>
                        <ul
                          role="group"
                          aria-labelledby="react-aria7472048721-:r28:"
                          className="data-[has-title=true]:pt-1"
                          data-has-title="true"
                          data-slot="group"
                        >
                          <li
                            role="menuitem"
                            aria-labelledby="react-aria7472048721-:r29:"
                            tabIndex={-1}
                            data-key="$.0.0"
                            className="flex group gap-2 items-center justify-between relative py-1.5 w-full h-full box-border rounded-small subpixel-antialiased cursor-pointer tap-highlight-transparent outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 data-[focus-visible=true]:dark:ring-offset-background-content1 border-small border-transparent data-[hover=true]:bg-default-100 data-[selectable=true]:focus:border-default data-[selectable=true]:focus:bg-default-100 data-[hover=true]:transition-colors data-[hover=true]:text-default-foreground data-[selectable=true]:focus:text-default-foreground hover:!bg-transparent hover:border-transparent hover:cursor-text"
                            data-focus="true"
                          >
                            <span
                              id="react-aria7472048721-:r29:"
                              className="flex-1 text-small font-normal"
                            >
                              <div className="flex flex-col space-y-2">
                                <p className="text-sm font-medium leading-none">
                                  {user?.name}
                                </p>
                              </div>
                            </span>
                          </li>
                          <li
                            className="shrink-0 bg-divider border-none w-full h-divider mt-2"
                            role="separator"
                          ></li>
                        </ul>
                      </li>
                      <li
                        role="menuitem"
                        aria-labelledby="react-aria7472048721-:r2f:"
                        tabIndex={-1}
                        data-key="$.2"
                        className="flex group gap-2 items-center justify-between relative py-1.5 w-full h-full box-border rounded-small subpixel-antialiased cursor-pointer tap-highlight-transparent outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 data-[focus-visible=true]:dark:ring-offset-background-content1 border-small border-transparent hover:border-default data-[hover=true]:bg-default-100 data-[selectable=true]:focus:border-default data-[selectable=true]:focus:bg-default-100 data-[hover=true]:transition-colors data-[hover=true]:text-default-foreground data-[selectable=true]:focus:text-default-foreground"
                      >
                        <span
                          id="react-aria7472048721-:r2f:"
                          className="flex-1 text-small font-normal hover:text-[#a1a1aa] transition-all duration-300"
                          onClick={handleLogout}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="lucide lucide-log-out w-4 h-4 mr-2 inline-block"
                          >
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                            <polyline points="16 17 21 12 16 7"></polyline>
                            <line x1="21" x2="9" y1="12" y2="12"></line>
                          </svg>
                          <span className="text-sm">Logout</span>
                        </span>
                      </li>
                    </ul>
                  </div>
                  <span data-focus-scope-end="true" hidden={true}></span>
                </div>
              </div>
              <div style={{
                border: "0px",
                clip: "rect(0px, 0px, 0px, 0px)",
                clipPath: "inset(50%)",
                height: "1px",
                margin: "-1px",
                overflow: "hidden",
                padding: "0px",
                position: "absolute",
                width: "1px",
                whiteSpace: "nowrap"
              }}>
                <button
                  id="react-aria7472048721-:r2i:"
                  aria-label="Dismiss"
                  tabIndex={-1}
                  style={{ width: "1px", height: "1px" }}
                ></button>
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  );
}

export default Header;
