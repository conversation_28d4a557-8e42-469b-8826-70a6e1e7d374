"use client";

import axios from 'axios';
import { useRouter } from 'next/navigation';
import { useEffect, useState, ReactNode } from 'react';

interface AuthCheckProps {
  children: ReactNode;
}

export default function AuthCheck({ children }: AuthCheckProps) {
  const [isAuthChecking, setIsAuthChecking] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Fetch authentication status
        const response = await axios.get('/api/me', {
          withCredentials: true,
        });
        console.log("response", response);
        if (response.status === 200) {
          setIsAuthenticated(true);
        } else {
          // If not authenticated, redirect to login
          router.push('/login');
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        // On error, redirect to login
        router.push('/login');
      } finally {
        setIsAuthChecking(false);
      }
    };

    checkAuth();
  }, [router]);

  if (isAuthChecking) {
    // Show loading state while checking authentication
    return <></>;
  }

  // If authenticated, render children
  return isAuthenticated ? <>{children}</> : null;
} 