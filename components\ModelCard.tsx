import { Model } from "@/types/Model";
import axios from "axios";

interface ModelCardProps {
  model: Model;
  title: string;
  status: string;
  plus?: boolean;
  currentModel: string;
}

function ModelCard({ model, title, status, currentModel }: ModelCardProps) {
  let label = "Free";
  let numberOfGreenBlocks = 0;

  if (model?.isPlus) {
    label = "Plus";
  } else if (model?.isPro) {
    label = "Pro";
  } else if (model?.isTeam) {
    label = "Team";
  } else if (model?.isMax) {
    label = "Max";
  }

  if (model?.count < 100) {
    numberOfGreenBlocks = 4;
  } else if (model?.count < 200) {
    numberOfGreenBlocks = 3;
  } else if (model?.count < 300) {
    numberOfGreenBlocks = 2;
  } else if (model?.count < 400) {
    numberOfGreenBlocks = 1;
  }

  const handleRedirectToModel = async () => {
    const modelID = model?.carID;

    try {
      await axios.post(
        `/api/session?modelID=${modelID}&currentModel=${currentModel}`
      );

      if (currentModel === "chatgpt") {
        window.open("https://ai.chatshare.icu/", "_blank");
      } else if (currentModel === "claude") {
        window.open("https://ai.chatshare.icu/new", "_blank");
      } else {
        window.open(`https://ai.chatshare.icu/${currentModel}`, "_blank");
      }
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div
      className="bg-[#f2f4f7] dark:bg-[#33353b] transition-all rounded-lg p-4 cursor-pointer flex flex-col gap-3 items-start text-sm border-[3px] border-transparent duration-300 hover:border-[#9a7cdf]"
      onClick={handleRedirectToModel}
    >
      <div className="flex items-center gap-2 w-full">
        <div className="relative max-w-fit min-w-min inline-flex items-center justify-between box-border whitespace-nowrap rounded-small capitalize text-white px-1 py-0.5 text-xs h-auto bg-[#9a7cdf] hover:bg-[#9a7cdf]/80 rounded-lg">
          <span className="flex-1 text-inherit px-1 font-bold">{label}</span>
        </div>
        <div className="truncate flex-1 font-semibold">
          {title} <span className="text-gray-400 text-xs">({model?.carID})</span>
        </div>
      </div>
      <div className="text-gray-400 text-xs">Status: {status}</div>
      <div className="flex items-center gap-1 h-2 w-full">
        {Array.from({ length: 4 - numberOfGreenBlocks }).map((_, index) => (
          <div
            key={index}
            className="flex-1 h-full rounded-full bg-[#f3b656]"
          ></div>
        ))}
        {Array.from({ length: numberOfGreenBlocks }).map((_, index) => (
          <div
            key={index}
            className="flex-1 h-full rounded-full bg-[#3fcfa4]"
          ></div>
        ))}
      </div>
    </div>
  );
}

export default ModelCard;
