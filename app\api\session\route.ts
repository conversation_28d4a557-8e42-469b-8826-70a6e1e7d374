import axios from "axios";

export async function POST(request: Request) {
  // Extract cookie from request headers
  const cookieHeader = request.headers.get('cookie') || '';
  const shareSessionMatch = cookieHeader.match(/share-session=([^;]+)/);
  const shareSessionValue = shareSessionMatch ? shareSessionMatch[1] : null;

  const { searchParams } = new URL(request.url);
  const modelID = searchParams.get("modelID");
  const currentModel = searchParams.get("currentModel");

  // If no session cookie, return unauthorized without making API call
  if (!shareSessionValue) {
    return new Response(
      JSON.stringify({ error: "No session found" }), 
      { 
        status: 401,
        headers: { "Content-Type": "application/json" }
      }
    );
  }

  try {
    const response = await axios.get(
      `${process.env.API_URL}/auth/loginSession?carid=${modelID}&carType=${currentModel}`,
      {
        headers: {
          Cookie: `share-session=${shareSessionValue}`,
        },
      }
    );

    return new Response(
      JSON.stringify(response.data), 
      { 
        status: 200,
        headers: { "Content-Type": "application/json" }
      }
    );
  } catch (error) {
    console.error("Error in /api/session:", error);
    
    // Include specific error details for debugging
    return new Response(
      JSON.stringify({
        error: "Failed to fetch session",
        message: error instanceof Error ? error.message : "Unknown error"
      }), 
      { 
        status: 500,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
}
