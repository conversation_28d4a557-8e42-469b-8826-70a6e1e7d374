import axios from "axios";
import { NextResponse } from "next/server";
import { cookies } from "next/headers";

export async function POST(
  req: Request
) {
  try {
    // Extract parameters from request body
    const { carid, carType } = await req.json();
    
    // Get cookies using await since it's now an async function
    const cookieStore = await cookies();
    const shareSession = cookieStore.get("share-session");

    const { data: modelData } = await axios.get(
      `${process.env.FRONTEND_API_URL}/auth/loginSession?carid=${carid}&carType=${carType}`,
      {
        headers: {
          "Content-Type": "application/json",
          "Cookie": `share-session=${shareSession?.value}`,
        },
      }
    );

    return NextResponse.json(modelData);
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: "Failed to fetch model data" }, { status: 500 });
  }
}
