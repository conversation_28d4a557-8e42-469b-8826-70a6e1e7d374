import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "react-icons/si";
import { Midjourney, Grok } from "@lobehub/icons";
import { FaRegCirclePlay } from "react-icons/fa6";

export const modelLogos = {
  chatgpt: {
    logo: SiOpenai,
    logoColor: '#fff',
    label: 'ChatGPT',
  },
  claude: {
    logo: <PERSON><PERSON><PERSON><PERSON>,
    logoColor: 'rgb(217, 119, 87)',
    label: '<PERSON>',
  },
  mj: {
    logo: Midjourney,
    logoColor: '#fff',
    label: 'Midjourney',
  },
  grok: {
    logo: Grok,
    logoColor: '#fff',
    label: 'Grok',
  },
  sora: {
    logo: FaRegCirclePlay,
    logoColor: '#fff',
    label: 'Sora',
  },
};
