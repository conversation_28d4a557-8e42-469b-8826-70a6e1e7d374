//app>api>me>route.ts
import axios from "axios";
import { cookies } from "next/headers";

export async function GET(request: Request) {
  // Extract cookie from request headers
  const cookieHeader = await cookies();
  const shareSessionValue = cookieHeader.get('share-session')?.value;

  console.log("shareSessionValue", shareSessionValue);

  // If no session cookie, return unauthorized without making API call
  if (!shareSessionValue) {
    return new Response(
      JSON.stringify({ error: "No session found" }), 
      { 
        status: 401,
        headers: { "Content-Type": "application/json" }
      }
    );
  }

  try {
    console.log("url", `${process.env.FRONTEND_API_URL}/getme`);

    const response = await axios.get(
      `${process.env.FRONTEND_API_URL}/getme`,
      {
        headers: {
          Cookie: `share-session=${shareSessionValue}`,
        },
      }
    );

    return new Response(
      JSON.stringify(response.data), 
      { 
        status: 200,
        headers: { "Content-Type": "application/json" }
      }
    );
  } catch (error) {
    console.error("Error in /api/me:", error);
    
    // Include a specific error message for debugging
    return new Response(
      JSON.stringify({ 
        error: "Authentication required",
        message: error instanceof Error ? error.message : "Unknown error"
      }), 
      { 
        status: 401,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
}
