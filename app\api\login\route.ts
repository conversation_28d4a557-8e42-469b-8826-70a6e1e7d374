import axios from "axios";

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { username, password } = body;

    // Call your external authentication API
    const response = await axios.post(
      `${process.env.FRONTEND_API_URL}/login`,
      {
        userToken: username,
        password: password,
        token: ""
      },
      {
        headers: {
          "Content-Type": "application/json"
        }
      }
    );

    // Extract the session cookie from response
    const setCookieHeader = response.headers["set-cookie"];
    let shareSessionCookie = "";
    
    if (setCookieHeader) {
      shareSessionCookie = setCookieHeader.find((cookie: string) => 
        cookie.startsWith("share-session=")
      ) || "";
    }

    // Create a response with the session cookie
    const responseHeaders = new Headers();
    responseHeaders.append("Content-Type", "application/json");
    
    if (shareSessionCookie) {
      // Add domain attribute to the cookie for subdomain access
      const domainName = process.env.DOMAIN_NAME;
      const enhancedCookie = shareSessionCookie.includes("Domain=") 
        ? shareSessionCookie 
        : `${shareSessionCookie}; Domain=${domainName}`;
      
      responseHeaders.append("Set-Cookie", enhancedCookie);
    }

    // Return success response with cookie header
    return new Response(
      JSON.stringify({ 
        success: true, 
        user: response.data.data
      }),
      {
        status: 200,
        headers: responseHeaders
      }
    );
  } catch (error: any) {
    console.error("Login error:", error);

    // Return error response
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.response?.data?.msg || "Login failed. Please try again." 
      }),
      {
        status: 401,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
}
