"use client";

import React, { useState, useEffect, useContext } from "react";
import Image from "next/image";
import axios from "axios";
import { useParams } from "next/navigation";

import AuthCheck from "@/components/AuthCheck";
import Avatar from "@/public/avatar.svg";
import ModelsContainer from "@/components/ModelsContainer";
import Header from "@/components/Header";
import ModelsContext from "@/context/ModelsContext";
import ModelCard from "@/components/ModelCard";
import { modelLogos } from "@/app/config/defaults";
import MobileModelsDropdown from "@/components/MobileModelsDropdown";
import UserContext from "@/context/UserContext";
// Define types
interface Config {
  data: {
    siteType: string;
  };
}

interface User {
  name: string;
}

// Main page component - now a client component
export default function CardPage() {
  const params = useParams();
  const {
    selectedModel,
    setSelectedModel,
    models,
    setModels,
    setIsLoading,
    isLoading,
  } = useContext(ModelsContext);

  const { user } = useContext(UserContext);

  const currentModel = Array.isArray(params.slug)
    ? params.slug[0]
    : typeof params.slug === "string"
    ? params.slug
    : "chatgpt";

  const [config, setConfig] = useState<Config | null>(null);
  const [loadingModels, setLoadingModels] = useState(false);

  // Initialize selectedModel if not set yet
  useEffect(() => {
    if (!selectedModel && currentModel) {
      setSelectedModel(currentModel);
    }
  }, [currentModel, selectedModel, setSelectedModel]);

  useEffect(() => {
    // Fetch config and user data
    const fetchData = async () => {
      try {
        // Client-side API request for config
        const configResponse = await axios.get("/api/config");
        if (configResponse.status === 200) {
          setConfig(configResponse.data);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const fetchModels = async () => {
      const modelToFetch = selectedModel || currentModel;

      if (modelToFetch) {
        // Set isLoading only for the content grid, not the sidebar
        setIsLoading(true);
        setLoadingModels(true);

        try {
          // Client-side API request for models
          const modelsResponse = await axios.get(
            `/api/models?model=${modelToFetch}`
          );
          if (modelsResponse.status === 200) {
            setModels(modelsResponse?.data?.data?.list || []);
          }
        } catch (error) {
          console.error("Error fetching models:", error);
          setModels([]);
        } finally {
          // Make sure loading state is turned off even on error
          setIsLoading(false);
          setLoadingModels(false);
        }
      }
    };

    fetchModels();
  }, [selectedModel, currentModel, setIsLoading, setModels]);

  return (
    <AuthCheck>
      <div className="min-h-screen flex flex-col fixed">
        <Header />
        <div className="flex flex-1 relative w-screen flex-col md:flex-row md:overflow-hidden overflow-y-auto">
          <div className="px-4 py-2 mt-20 md:hidden">
            <MobileModelsDropdown config={config} currentModel={currentModel} />
          </div>
          <aside className="bg-black text-white px-6 pb-0 border-white/10 md:fixed md:flex hidden flex-col top-[72px] md:h-[calc(100vh-72px)] h-auto overflow-y-auto z-40 md:w-[304px] w-full md:pt-6 pt-22">
            <div className="bg-zinc-900 rounded-xl p-6 mb-6 border min-h-[270px] min-w-[245px] border-white/10 flex-col justify-between hidden md:flex">
              <div>
                <p className="text-l">Welcome back</p>
                <p className="text-sm text-gray-400 mb-4">
                  ChatShare provides Plus and Claude services, welcome to use
                  them
                </p>
              </div>
              <div className="flex items-center justify-center">
                <Image src={Avatar} alt="Avatar" width={80} height={80} />
              </div>
              {user && (
                <div className="flex flex-col items-center">
                  <p className="text-white font-semibold text-xl">
                    {user.name}
                  </p>
                </div>
              )}
            </div>

            <div className="hidden md:block">
              <ModelsContainer config={config} currentModel={currentModel} />
            </div>
          </aside>
          <div className="md:fixed md:w-auto top-[72px] left-72 right-0 bottom-0 overflow-y-auto flex flex-col w-full">
            <div className="p-6 md:min-h-[calc(100vh-72px)] min-h-auto w-full">
              <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-2 relative">
                {isLoading && (
                  <div
                    className="absolute inset-0 bg-black/40 backdrop-blur-sm z-10 flex items-center justify-center transition-all duration-300 ease-in-out"
                    style={{
                      opacity: isLoading ? 1 : 0,
                      pointerEvents: isLoading ? "auto" : "none",
                    }}
                  >
                    <div className="flex flex-col items-center gap-3">
                      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
                      <p className="text-white text-sm">
                        Loading {selectedModel}...
                      </p>
                    </div>
                  </div>
                )}
                {models.map((model, idx) => (
                  <ModelCard
                    key={`model-${idx}`}
                    model={model}
                    currentModel={selectedModel || currentModel}
                    title={
                      modelLogos[
                        (selectedModel ||
                          currentModel) as keyof typeof modelLogos
                      ]?.label || ""
                    }
                    status={
                      model?.count < 300 ? "Recommend" : "Not Recommend"
                    }
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </AuthCheck>
  );
}
