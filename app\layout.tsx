"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";

import FirstGlowImage from "@/public/background-BiePeNN8.avif";
import SecondGlowImage from "@/public/background-dark-Bsq-hjJH.avif";
import ModelsContext from "@/context/ModelsContext";

import "./globals.css";
import { Model } from "@/types/Model";
import axios from "axios";
import UserContext from "@/context/UserContext";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [selectedModel, setSelectedModel] = useState<string>("");
  const [models, setModels] = useState<Model[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [user, setUser] = useState<User.User | null>(null);
  const [isUserLoading, setIsUserLoading] = useState<boolean>(false);

  useEffect(() => {
    if (!document.cookie.includes("share-session")) return;

    const fetchUser = async () => {
      setIsUserLoading(true);
      try {
        const userResponse = await axios.get("/api/me");
        if (userResponse.status === 200) {
          setUser(userResponse.data?.data);
        }
      } catch (error) {
        console.error("Error fetching user:", error);
        setUser(null);
      }
      setIsUserLoading(false);
    };
    fetchUser();
  }, []);

  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <UserContext.Provider value={{ user, setUser, isLoading: isUserLoading }}>
          <ModelsContext.Provider
            value={{
              selectedModel,
              setSelectedModel,
              models,
              setModels,
              isLoading,
              setIsLoading,
            }}
          >
            <div className="relative h-screen overflow-hidden">
              <div className="absolute z-20 top-0 inset-x-0 flex justify-center overflow-hidden pointer-events-none">
                <div className="w-[108rem] flex-none flex justify-end">
                  <picture>
                    <source srcSet={FirstGlowImage.src} type="image/avif" />
                    <img
                      src={FirstGlowImage.src}
                      alt=""
                      className="w-[71.75rem] flex-none max-w-none dark:hidden"
                      decoding="async"
                    />
                  </picture>
                  <picture>
                    <source srcSet={SecondGlowImage.src} type="image/avif" />
                    <img
                      src={SecondGlowImage.src}
                      alt=""
                      className="w-[90rem] flex-none max-w-none hidden dark:block"
                      decoding="async"
                    />
                  </picture>
                </div>
              </div>
              {children}
            </div>
          </ModelsContext.Provider>
        </UserContext.Provider>
      </body>
    </html>
  );
}
