"use client";

import React, { useContext } from "react";
import ModelsList from "@/components/ModelList";
import ModelCard from "@/components/ModelCard";
import { modelLogos } from "@/app/config/defaults";
import ModelsContext from "@/context/ModelsContext";

interface Config {
  data: {
    siteType: string;
  };
}

interface ModelsContainerProps {
  config: Config | null;
  currentModel: string;
}

export default function ModelsContainer({
  config,
  currentModel,
}: ModelsContainerProps) {
  const { selectedModel, models, isLoading, setSelectedModel } = useContext(ModelsContext);

  const handleModelSelect = (model: string) => {
    if (model !== selectedModel) {
      setSelectedModel(model);
    }
  };

  return (
    <>
      <div>
        <h3 className="text-xl text-white mb-4">Service Area</h3>
        <ModelsList
          config={config}
          currentModel={selectedModel || currentModel}
          onModelSelect={handleModelSelect}
          isLoading={isLoading}
        />
      </div>
    </>
  );
}
