import { Model } from '@/types/Model';
import { createContext } from 'react';

interface ModelsContextType {
    selectedModel: string;
    setSelectedModel: (model: string) => void;
    models: Model[];
    setModels: (models: Model[]) => void;
    isLoading: boolean;
    setIsLoading: (isLoading: boolean) => void;
}

const ModelsContext = createContext<ModelsContextType>({
    selectedModel: '',
    setSelectedModel: () => {},
    models: [],
    setModels: () => {},
    isLoading: false,
    setIsLoading: () => {},
});

export default ModelsContext;