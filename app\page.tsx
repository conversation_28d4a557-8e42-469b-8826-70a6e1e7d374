import Link from 'next/link';

export default function HomePage() {
  const prompts = [
    "Write a text message inviting a friend to be a wedding plus one↗",
    "Give me some ideas for handling children's artwork↗",
    "Help me learn vocabulary for the college entrance exam↗",
    "Write a message with a kitten GIF for a friend going through a tough time↗",
    "Test my knowledge of ancient civilizations↗",
    "Create a story about the toothbrush shark superhero Sharky↗",
    "Plan a trip to Seoul to experience it like a local↗",
    "Create a content calendar for a TikTok account↗",
    "Improve my thesis writing, ask me to list my ideas↗",
    "Create a personal webpage for me after asking me three questions↗",
    "Make a sandwich using the ingredients in my kitchen↗",
    "Test my knowledge of world capitals to enhance my geography skills↗",
    "Tell me about the Roman Empire↗",
    "Python script to automatically send daily email reports↗",
    "Ask me three questions then create a personal webpage for me↗",
    "Create a morning routine to boost my productivity↗",
    "Plan a mental health day to help me relax↗",
    "Design a programming game to teach basic knowledge and make learning fun↗",
    "Suggest fun activities to help me make friends in a new city↗",
    "You can find inspiration on ChatShare↗",
    "Create a charter for starting a film club↗",
    "Passion!↗",
    "Cook something using my high-quality ingredients↗",
    "Summer!↗",
    "Give me some ideas on how to plan New Year's resolutions↗",
  ];

  // prompts Three Sections
  const firstRow = prompts.slice(0, Math.ceil(prompts.length / 3));
  const secondRow = prompts.slice(Math.ceil(prompts.length / 3), Math.ceil((prompts.length * 2) / 3));
  const thirdRow = prompts.slice(Math.ceil((prompts.length * 2) / 3));

  return (
    <div className="min-h-screen bg-gradient-to-r text-white flex flex-col items-center justify-center">
      <div className="text-center mt-24 mb-32 w-full max-w-2xl">
        <h1 className="text-[32px] sm:text-[40px] md:text-[48px] lg:text-[54.4px] leading-[1.2] font-serif text-center mb-5 text-[rgb(249,249,249)] text-balance">
          Answer your questions, find writing inspiration, and improve work efficiency
        </h1>
        <p className="text-[16px] leading-[24px] font-sans text-center mb-6 text-[rgb(249,249,249)] text-balance">
          Just ask, and AI can help you write, learn, brainstorm, and more.
        </p>
        <Link href="/login">
          <button className="bg-white text-black px-6 py-3 rounded-full font-semibold hover:bg-gray-200 transition cursor-pointer">
            Get Started ↗
          </button>
        </Link>
      </div>

     
      <div className="w-full overflow-hidden py-2">
        <div className="animate-marquee-right whitespace-nowrap flex items-center">
            {[...firstRow, ...firstRow].map((text, index) => (
            <div
              key={`row1-${index}`}
              className="inline-block bg-[var(--dark-gray)] hover:underline transition p-6 rounded-lg text-white shadow mx-2 cursor-pointer"
            >
              {text}
            </div>
          ))}
        </div>
      </div>

      
      <div className="w-full overflow-hidden py-2">
        <div className="animate-marquee-left whitespace-nowrap flex items-center">
        {[...secondRow, ...secondRow].map((text, index) => (
            <div
              key={`row2-${index}`}
              className="inline-block bg-[var(--dark-gray)] hover:underline transition p-6 rounded-lg text-white shadow mx-2 cursor-pointer"
            >
              {text}
            </div>
          ))}
        </div>
      </div>

      
      <div className="w-full overflow-hidden py-2">
        <div className="animate-marquee-right whitespace-nowrap flex items-center">
        {[...thirdRow, ...thirdRow].map((text, index) =>  (
            <div
              key={`row3-${index}`}
              className="inline-block bg-[var(--dark-gray)] hover:underline transition p-6 rounded-lg text-white shadow mx-2 cursor-pointer"
            >
              {text}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
} 
