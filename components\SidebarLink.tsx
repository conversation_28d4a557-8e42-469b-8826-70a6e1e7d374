"use client";

import Link from 'next/link';
import React, { useContext } from 'react'
import { IconType } from 'react-icons';
import ModelsContext from '@/context/ModelsContext';

interface SidebarLinkProps {
  href: string;
  Icon: IconType;
  label: string;
  isActive?: boolean;
  logoColor?: string;
  onClick?: () => void;
  disabled?: boolean;
  model?: string;
}

function SidebarLink({ 
  href, 
  Icon, 
  label, 
  isActive = false, 
  logoColor = 'white', 
  onClick,
  disabled = false,
  model
}: SidebarLinkProps) {
  const { setSelectedModel } = useContext(ModelsContext);
  let logoColorStyle = '#fff';

  if (logoColor === '#fff') {
    if (isActive) {
      logoColorStyle = '#000';
    } else {
      logoColorStyle = '#fff';
    }
  } else {
    logoColorStyle = logoColor;
  }

  const handleClick = (e: React.MouseEvent) => {  
    if (model) {
      setSelectedModel(model);
      if (onClick) {
        onClick();
      }
    }
  };

  return (
    <li className={`
      ${isActive ? 'bg-white rounded-md text-black' : 'text-white hover:bg-white/10 rounded-md transition-colors duration-200'}
      ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
    `}>
      <div 
        // href={disabled ? '#' : href}
        className={`font-medium px-3 py-2 rounded flex items-center gap-2 w-full ${disabled ? 'pointer-events-none' : 'cursor-pointer'}`}
        onClick={handleClick}
      >
        <Icon style={{ color: logoColorStyle }} size={18} />
        <span className={`${isActive ? 'text-black' : 'text-white'}`}>{label}</span>
      </div>
    </li>
  )
}

export default SidebarLink