import axios from "axios";
import { cookies } from "next/headers";

const models = {
  chatgpt: "carpage",
  claude: "claudeCarpage",
  midjourney: "mjCarpage",
  grok: "grokCarpage",
  sora: "soraCarpage",
};

export async function GET(req: Request) {
  const cookieStore = await cookies();
  const shareSession = cookieStore.get("share-session");

  const { searchParams } = new URL(req.url);
  const model = searchParams.get("model");

  if (!model) {
    return new Response("Model not found", { status: 404 });
  }

  const pageName = models[model as keyof typeof models];

  try {
    const response = await axios.post(
      `${process.env.FRONTEND_API_URL}/${pageName}`,
      {},
      {
        headers: {
          Cookie: `share-session=${shareSession?.value}`,
        },
      }
    );

    return new Response(JSON.stringify(response.data), { status: 200 });
  } catch (error) {
    return new Response(JSON.stringify(error), { status: 500 });
  }
}
